import { useState, useEffect } from "react";
import Chart from "react-apexcharts";
import { ApexOptions } from "apexcharts";
import { Dropdown } from "../ui/dropdown/Dropdown";
import { DropdownItem } from "../ui/dropdown/DropdownItem";
import { MoreDotIcon } from "../../icons";
import axios from "axios";
import {
  calculateRSI,
  calculateSMA,
  calculateEMA,
  calculateWMA,
  calculateMACD,
  calculateBollingerBands,
  formatIndicator<PERSON>or<PERSON>hart,
  formatMACDFor<PERSON>hart,
  formatBollingerBandsForChart,
  DEFAULT_INDICATOR_CONFIG,
  COLOR_SCHEMES,
  applyColorScheme,
  type CandleData,
  type IndicatorData,
  type MACDData,
  type BollingerBandsData
} from "../../utils/technicalIndicators";

// Interface for Binance kline data (extending CandleData for compatibility)
interface KlineData extends CandleData {
  x: Date;
  y: [number, number, number, number]; // [open, high, low, close]
}

// Interface for indicator configuration
interface IndicatorConfig {
  rsi: { period: number; enabled: boolean; color: string };
  sma: { period: number; enabled: boolean; color: string };
  ema: { period: number; enabled: boolean; color: string };
  wma: { period: number; enabled: boolean; color: string };
  macd: {
    fastPeriod: number;
    slowPeriod: number;
    signalPeriod: number;
    enabled: boolean;
    colors: { macd: string; signal: string; histogram: string }
  };
  bollingerBands: {
    period: number;
    stdDev: number;
    enabled: boolean;
    colors: { upper: string; middle: string; lower: string }
  };
}

// Format Binance klines data for ApexCharts
const formatKlinesData = (data: any[], displayLimit: number = 100): KlineData[] => {
  if (!data || !Array.isArray(data)) return [];

  // Calculate the maximum period needed for technical indicators
  const maxIndicatorPeriod = Math.max(
    DEFAULT_INDICATOR_CONFIG.sma.period,
    DEFAULT_INDICATOR_CONFIG.ema.period,
    DEFAULT_INDICATOR_CONFIG.wma.period,
    DEFAULT_INDICATOR_CONFIG.bollingerBands.period,
    DEFAULT_INDICATOR_CONFIG.macd.slowPeriod + DEFAULT_INDICATOR_CONFIG.macd.signalPeriod
  );

  // Fetch extra data for indicator calculations but only display the requested amount
  const totalDataNeeded = displayLimit + maxIndicatorPeriod;
  const recentData = data.slice(-totalDataNeeded);

  return recentData.map(kline => ({
    x: new Date(kline[0]), // openTime
    y: [
      parseFloat(kline[1]), // open
      parseFloat(kline[2]), // high
      parseFloat(kline[3]), // low
      parseFloat(kline[4])  // close
    ]
  }));
};

export default function BitcoinCandlestickChart() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedInterval, setSelectedInterval] = useState<string>("1d");
  const [candlestickData, setCandlestickData] = useState<KlineData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Technical indicators state
  const [indicatorConfig, setIndicatorConfig] = useState<IndicatorConfig>(DEFAULT_INDICATOR_CONFIG);
  const [showIndicatorPanel, setShowIndicatorPanel] = useState(false);
  const [selectedColorScheme, setSelectedColorScheme] = useState<string>('default');

  // Fetch klines data from the backend API
  const fetchKlinesData = async (interval: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`http://localhost:8000/api/binance-klines/?interval=${interval}`);

      if (response.data && response.data.klines) {
        // Set display limits based on the interval
        let displayLimit = 100;
        if (interval === '1m') displayLimit = 60;
        if (interval === '5m') displayLimit = 72;
        if (interval === '15m') displayLimit = 96;

        // Format data with extra historical data for indicator calculations
        const allFormattedData = formatKlinesData(response.data.klines, displayLimit);

        // Store all data for indicator calculations
        setCandlestickData(allFormattedData);
      } else {
        setError("Invalid data format received from API");
      }
    } catch (err) {
      console.error('Failed to fetch klines data:', err);
      setError('Failed to load candlestick data');
    } finally {
      setIsLoading(false);
    }
  };

  // Update data when interval changes
  const handleIntervalChange = (interval: string) => {
    setSelectedInterval(interval);
    fetchKlinesData(interval);
  };

  // Refresh data with current interval
  const refreshData = () => {
    fetchKlinesData(selectedInterval);
  };

  // Calculate technical indicators
  const calculateIndicators = () => {
    if (candlestickData.length === 0) return [];

    const indicators: any[] = [];
    const displayLimit = getDisplayLimit(selectedInterval);

    // Get the time range for the displayed candles to filter indicators
    const displayCandlestickData = candlestickData.slice(-displayLimit);
    const displayStartTime = displayCandlestickData.length > 0 ? displayCandlestickData[0].x.getTime() : 0;

    // Add moving averages to main chart with enhanced styling
    if (indicatorConfig.sma.enabled) {
      const smaData = calculateSMA(candlestickData, indicatorConfig.sma.period);
      // Filter SMA data to match the display window
      const filteredSmaData = smaData.filter(point => point.x.getTime() >= displayStartTime);

      indicators.push({
        ...formatIndicatorForChart(filteredSmaData, `SMA(${indicatorConfig.sma.period})`),
        type: 'line',
        color: indicatorConfig.sma.color,
        yAxisIndex: 0,
        stroke: {
          width: 3,
          dashArray: 0
        },
        markers: {
          size: 0,
          hover: {
            size: 6
          }
        }
      });
    }

    if (indicatorConfig.ema.enabled) {
      const emaData = calculateEMA(candlestickData, indicatorConfig.ema.period);
      // Filter EMA data to match the display window
      const filteredEmaData = emaData.filter(point => point.x.getTime() >= displayStartTime);

      indicators.push({
        ...formatIndicatorForChart(filteredEmaData, `EMA(${indicatorConfig.ema.period})`),
        type: 'line',
        color: indicatorConfig.ema.color,
        yAxisIndex: 0,
        stroke: {
          width: 3,
          dashArray: 4 // Dashed line for EMA
        },
        markers: {
          size: 0,
          hover: {
            size: 6
          }
        }
      });
    }

    if (indicatorConfig.wma.enabled) {
      const wmaData = calculateWMA(candlestickData, indicatorConfig.wma.period);
      // Filter WMA data to match the display window
      const filteredWmaData = wmaData.filter(point => point.x.getTime() >= displayStartTime);

      indicators.push({
        ...formatIndicatorForChart(filteredWmaData, `WMA(${indicatorConfig.wma.period})`),
        type: 'line',
        color: indicatorConfig.wma.color,
        yAxisIndex: 0,
        stroke: {
          width: 3,
          dashArray: 8 // Dotted line for WMA
        },
        markers: {
          size: 0,
          hover: {
            size: 6
          }
        }
      });
    }

    // Add Bollinger Bands to main chart with enhanced styling
    if (indicatorConfig.bollingerBands.enabled) {
      const bbData = calculateBollingerBands(candlestickData, indicatorConfig.bollingerBands.period, indicatorConfig.bollingerBands.stdDev);
      // Filter Bollinger Bands data to match the display window
      const filteredBbData = bbData.filter(point => point.x.getTime() >= displayStartTime);
      const bbSeries = formatBollingerBandsForChart(filteredBbData);

      indicators.push({
        ...bbSeries[0], // Upper band
        type: 'line',
        color: indicatorConfig.bollingerBands.colors.upper,
        yAxisIndex: 0,
        stroke: {
          width: 1.5,
          dashArray: 3
        },
        fill: {
          opacity: 0.1
        }
      });

      indicators.push({
        ...bbSeries[1], // Middle band
        type: 'line',
        color: indicatorConfig.bollingerBands.colors.middle,
        yAxisIndex: 0,
        stroke: {
          width: 2,
          dashArray: 0
        }
      });

      indicators.push({
        ...bbSeries[2], // Lower band
        type: 'line',
        color: indicatorConfig.bollingerBands.colors.lower,
        yAxisIndex: 0,
        stroke: {
          width: 1.5,
          dashArray: 3
        },
        fill: {
          opacity: 0.1
        }
      });
    }

    return indicators;
  };

  // Calculate oscillator indicators (RSI, MACD) for separate chart
  const calculateOscillators = () => {
    if (candlestickData.length === 0) return [];

    const oscillators: any[] = [];
    const displayLimit = getDisplayLimit(selectedInterval);

    // Get the time range for the displayed candles to filter indicators
    const displayCandlestickData = candlestickData.slice(-displayLimit);
    const displayStartTime = displayCandlestickData.length > 0 ? displayCandlestickData[0].x.getTime() : 0;

    try {
      if (indicatorConfig.rsi.enabled) {
        const rsiData = calculateRSI(candlestickData, indicatorConfig.rsi.period);
        // Filter RSI data to match the display window
        const filteredRsiData = rsiData.filter(point => point.x.getTime() >= displayStartTime);

        if (filteredRsiData.length > 0) {
          oscillators.push({
            ...formatIndicatorForChart(filteredRsiData, `RSI(${indicatorConfig.rsi.period})`),
            type: 'line',
            color: indicatorConfig.rsi.color,
            stroke: {
              width: 3,
              curve: 'smooth'
            }
          });
        }
      }

      if (indicatorConfig.macd.enabled) {
        const macdData = calculateMACD(
          candlestickData,
          indicatorConfig.macd.fastPeriod,
          indicatorConfig.macd.slowPeriod,
          indicatorConfig.macd.signalPeriod
        );

        // Filter MACD data to match the display window
        const filteredMacdData = macdData.filter(point => point.x.getTime() >= displayStartTime);

        if (filteredMacdData.length > 0) {
          const macdSeries = formatMACDForChart(filteredMacdData);

          // Only add series with valid data and enhanced styling
          if (macdSeries[0]?.data?.length > 0) {
            oscillators.push({
              ...macdSeries[0], // MACD line
              type: 'line',
              color: indicatorConfig.macd.colors.macd,
              stroke: {
                width: 2.5,
                curve: 'smooth'
              }
            });
          }

          if (macdSeries[1]?.data?.length > 0) {
            oscillators.push({
              ...macdSeries[1], // Signal line
              type: 'line',
              color: indicatorConfig.macd.colors.signal,
              stroke: {
                width: 2,
                curve: 'smooth',
                dashArray: 5
              }
            });
          }

          if (macdSeries[2]?.data?.length > 0) {
            oscillators.push({
              ...macdSeries[2], // Histogram
              type: 'column',
              color: indicatorConfig.macd.colors.histogram,
              fill: {
                opacity: 0.7
              }
            });
          }
        }
      }
    } catch (error) {
      console.error('Error calculating oscillators:', error);
    }

    return oscillators;
  };

  // Toggle indicator
  const toggleIndicator = (indicator: keyof IndicatorConfig) => {
    setIndicatorConfig(prev => ({
      ...prev,
      [indicator]: {
        ...prev[indicator],
        enabled: !prev[indicator].enabled
      }
    }));
  };

  // Change color scheme
  const changeColorScheme = (schemeName: string) => {
    setSelectedColorScheme(schemeName);
    if (schemeName === 'default') {
      setIndicatorConfig(prev => ({
        ...DEFAULT_INDICATOR_CONFIG,
        // Preserve enabled states
        rsi: { ...DEFAULT_INDICATOR_CONFIG.rsi, enabled: prev.rsi.enabled },
        sma: { ...DEFAULT_INDICATOR_CONFIG.sma, enabled: prev.sma.enabled },
        ema: { ...DEFAULT_INDICATOR_CONFIG.ema, enabled: prev.ema.enabled },
        wma: { ...DEFAULT_INDICATOR_CONFIG.wma, enabled: prev.wma.enabled },
        macd: { ...DEFAULT_INDICATOR_CONFIG.macd, enabled: prev.macd.enabled },
        bollingerBands: { ...DEFAULT_INDICATOR_CONFIG.bollingerBands, enabled: prev.bollingerBands.enabled }
      }));
    } else {
      const newScheme = applyColorScheme(schemeName as keyof typeof COLOR_SCHEMES);
      setIndicatorConfig(prev => ({
        // Apply new colors but preserve enabled states
        rsi: { ...newScheme.rsi, enabled: prev.rsi.enabled },
        sma: { ...newScheme.sma, enabled: prev.sma.enabled },
        ema: { ...newScheme.ema, enabled: prev.ema.enabled },
        wma: { ...newScheme.wma, enabled: prev.wma.enabled },
        macd: { ...newScheme.macd, enabled: prev.macd.enabled },
        bollingerBands: { ...newScheme.bollingerBands, enabled: prev.bollingerBands.enabled }
      }));
    }
  };

  // Fetch data on component mount and when interval changes
  useEffect(() => {
    fetchKlinesData(selectedInterval);
  }, []);

  function toggleDropdown() {
    setIsOpen(!isOpen);
  }

  function closeDropdown() {
    setIsOpen(false);
  }

  // Get indicators and oscillators
  const indicators = calculateIndicators();
  const oscillators = calculateOscillators();
  const hasOscillators = oscillators.length > 0;

  const options: ApexOptions = {
    chart: {
      type: 'candlestick',
      height: hasOscillators ? 500 : 450,
      fontFamily: "Outfit, sans-serif",
      toolbar: {
        show: false,
      },
      background: 'transparent',
      animations: {
        enabled: true,
        easing: 'easeinout',
        speed: 800
      }
    },
    title: {
      text: `BTC/USDT (${selectedInterval})`,
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#6B7280'
      }
    },
    xaxis: {
      type: 'datetime',
      labels: {
        datetimeUTC: false,
        style: {
          colors: '#6B7280',
        }
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      tooltip: {
        enabled: true
      },
      labels: {
        style: {
          colors: '#6B7280',
        },
        formatter: (value) => {
          return value.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          });
        }
      }
    },
    grid: {
      borderColor: '#e0e0e0',
      strokeDashArray: 5,
      xaxis: {
        lines: {
          show: false
        }
      },
      yaxis: {
        lines: {
          show: true
        }
      },
    },
    tooltip: {
      enabled: true,
      theme: 'light',
      x: {
        format: selectedInterval === '1d' ? 'MMM dd' :
                selectedInterval === '4h' || selectedInterval === '1h' ? 'MMM dd HH:mm' :
                'MMM dd HH:mm:ss'
      },
    },
    plotOptions: {
      candlestick: {
        colors: {
          upward: '#12B76A', // Green for upward candles
          downward: '#F04438' // Red for downward candles
        },
        wick: {
          useFillColor: true,
        }
      }
    },
    stroke: {
      width: [1, 2, 2, 2, 2, 2], // Different widths for different series
      curve: 'smooth'
    },
    legend: {
      show: true,
      position: 'top',
      horizontalAlign: 'left',
      floating: false,
      fontSize: '12px',
      fontFamily: 'Outfit, sans-serif',
      markers: {
        width: 12,
        height: 12,
        strokeWidth: 0,
        strokeColor: '#fff',
        fillColors: undefined,
        radius: 2,
        customHTML: undefined,
        onClick: undefined,
        offsetX: 0,
        offsetY: 0
      },
      itemMargin: {
        horizontal: 8,
        vertical: 2
      }
    }
  };

  // Calculate display limits for consistent chart appearance
  const getDisplayLimit = (interval: string) => {
    if (interval === '1m') return 60;
    if (interval === '5m') return 72;
    if (interval === '15m') return 96;
    return 100;
  };

  const displayLimit = getDisplayLimit(selectedInterval);

  // Only display the most recent candles for the chart, but use all data for indicators
  const displayCandlestickData = candlestickData.slice(-displayLimit);

  const series = [
    {
      name: 'BTC/USDT',
      type: 'candlestick',
      data: displayCandlestickData
    },
    ...indicators
  ];

  // Interval options
  const intervals = [
    { value: '1m', label: '1m' },
    { value: '5m', label: '5m' },
    { value: '15m', label: '15m' },
    { value: '1h', label: '1h' },
    { value: '4h', label: '4h' },
    { value: '1d', label: '1d' }
  ];

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Candlestick Chart
          </h3>
        </div>
        <div className="flex items-start w-full gap-3 sm:justify-end">
          <div className="flex items-center gap-2 rounded-lg bg-gray-100 p-1 dark:bg-gray-900">
            {intervals.map((item) => (
              <button
                key={item.value}
                onClick={() => handleIntervalChange(item.value)}
                className={`px-3 py-1.5 font-medium rounded-md text-theme-sm hover:text-gray-900 dark:hover:text-white ${
                  selectedInterval === item.value
                    ? "shadow-theme-xs text-gray-900 dark:text-white bg-white dark:bg-gray-800"
                    : "text-gray-500 dark:text-gray-400"
                }`}
              >
                {item.label}
              </button>
            ))}
          </div>
          <button
            onClick={() => setShowIndicatorPanel(!showIndicatorPanel)}
            className={`px-3 py-1.5 font-medium rounded-md text-theme-sm hover:text-gray-900 dark:hover:text-white ${
              showIndicatorPanel
                ? "shadow-theme-xs text-gray-900 dark:text-white bg-white dark:bg-gray-800"
                : "text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-900"
            }`}
          >
            Indicators
          </button>
          <div className="relative inline-block">
            <button className="dropdown-toggle" onClick={toggleDropdown}>
              <MoreDotIcon className="text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 size-6" />
            </button>
            <Dropdown
              isOpen={isOpen}
              onClose={closeDropdown}
              className="w-40 p-2"
            >
              <DropdownItem
                onItemClick={() => {
                  refreshData();
                  closeDropdown();
                }}
              >
                Refresh
              </DropdownItem>
            </Dropdown>
          </div>
        </div>
      </div>

      {/* Technical Indicators Panel */}
      {showIndicatorPanel && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-sm font-semibold text-gray-800 dark:text-white">Technical Indicators</h4>

            {/* Color Scheme Selector */}
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-600 dark:text-gray-400">Color Scheme:</span>
              <select
                value={selectedColorScheme}
                onChange={(e) => changeColorScheme(e.target.value)}
                className="text-xs px-2 py-1 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
              >
                <option value="default">Default</option>
                <option value="ultraVibrant">Ultra Vibrant</option>
                <option value="neon">Neon</option>
                <option value="warm">Warm Colors</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {/* Moving Averages */}
            <div className="space-y-2">
              <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400">Moving Averages</h5>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={indicatorConfig.sma.enabled}
                  onChange={() => toggleIndicator('sma')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div
                  className="w-3 h-3 rounded-full border border-gray-300"
                  style={{ backgroundColor: indicatorConfig.sma.color }}
                ></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">SMA(20)</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={indicatorConfig.ema.enabled}
                  onChange={() => toggleIndicator('ema')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div
                  className="w-3 h-3 rounded-full border border-gray-300"
                  style={{ backgroundColor: indicatorConfig.ema.color }}
                ></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">EMA(20)</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={indicatorConfig.wma.enabled}
                  onChange={() => toggleIndicator('wma')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div
                  className="w-3 h-3 rounded-full border border-gray-300"
                  style={{ backgroundColor: indicatorConfig.wma.color }}
                ></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">WMA(20)</span>
              </label>
            </div>

            {/* Bollinger Bands */}
            <div className="space-y-2">
              <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400">Volatility</h5>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={indicatorConfig.bollingerBands.enabled}
                  onChange={() => toggleIndicator('bollingerBands')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div className="flex space-x-1">
                  <div
                    className="w-2 h-3 border border-gray-300"
                    style={{ backgroundColor: indicatorConfig.bollingerBands.colors.upper }}
                  ></div>
                  <div
                    className="w-2 h-3 border border-gray-300"
                    style={{ backgroundColor: indicatorConfig.bollingerBands.colors.middle }}
                  ></div>
                  <div
                    className="w-2 h-3 border border-gray-300"
                    style={{ backgroundColor: indicatorConfig.bollingerBands.colors.lower }}
                  ></div>
                </div>
                <span className="text-xs text-gray-700 dark:text-gray-300">Bollinger Bands</span>
              </label>
            </div>

            {/* Oscillators */}
            <div className="space-y-2">
              <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400">Oscillators</h5>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={indicatorConfig.rsi.enabled}
                  onChange={() => toggleIndicator('rsi')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div
                  className="w-3 h-3 rounded-full border border-gray-300"
                  style={{ backgroundColor: indicatorConfig.rsi.color }}
                ></div>
                <span className="text-xs text-gray-700 dark:text-gray-300">RSI(14)</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={indicatorConfig.macd.enabled}
                  onChange={() => toggleIndicator('macd')}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div className="flex space-x-1">
                  <div
                    className="w-2 h-3 border border-gray-300"
                    style={{ backgroundColor: indicatorConfig.macd.colors.macd }}
                  ></div>
                  <div
                    className="w-2 h-3 border border-gray-300"
                    style={{ backgroundColor: indicatorConfig.macd.colors.signal }}
                  ></div>
                  <div
                    className="w-2 h-3 border border-gray-300"
                    style={{ backgroundColor: indicatorConfig.macd.colors.histogram }}
                  ></div>
                </div>
                <span className="text-xs text-gray-700 dark:text-gray-300">MACD</span>
              </label>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-full overflow-x-auto custom-scrollbar">
        <div className="min-w-[1000px] xl:min-w-full">
          {error ? (
            <div className="flex items-center justify-center h-[450px] text-red-500">
              {error}
            </div>
          ) : isLoading ? (
            <div className="flex items-center justify-center h-[450px]">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : candlestickData.length === 0 ? (
            <div className="flex items-center justify-center h-[450px] text-gray-500">
              No data available for the selected interval
            </div>
          ) : (
            <>
              {/* Main Candlestick Chart */}
              <Chart
                options={options}
                series={series}
                type="candlestick"
                height={hasOscillators ? 500 : 450}
              />

              {/* Oscillators Chart */}
              {hasOscillators && oscillators.length > 0 && (
                <div className="mt-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    Technical Oscillators
                  </div>
                  <Chart
                    options={{
                      chart: {
                        type: 'line',
                        height: 250,
                        fontFamily: "Outfit, sans-serif",
                        toolbar: {
                          show: false,
                        },
                        background: 'transparent',
                      },
                      title: {
                        text: '',
                        align: 'left'
                      },
                      xaxis: {
                        type: 'datetime',
                        labels: {
                          datetimeUTC: false,
                          style: {
                            colors: '#6B7280',
                          }
                        },
                        axisBorder: {
                          show: false,
                        },
                        axisTicks: {
                          show: false,
                        },
                      },
                      yaxis: {
                        labels: {
                          style: {
                            colors: '#6B7280',
                          },
                          formatter: (value) => {
                            return value ? value.toFixed(2) : '0.00';
                          }
                        }
                      },
                      grid: {
                        borderColor: '#e0e0e0',
                        strokeDashArray: 5,
                        xaxis: {
                          lines: {
                            show: false
                          }
                        },
                        yaxis: {
                          lines: {
                            show: true
                          }
                        },
                      },
                      stroke: {
                        curve: 'smooth',
                        width: 2
                      },
                      tooltip: {
                        enabled: true,
                        theme: 'light',
                        x: {
                          format: selectedInterval === '1d' ? 'MMM dd' :
                                  selectedInterval === '4h' || selectedInterval === '1h' ? 'MMM dd HH:mm' :
                                  'MMM dd HH:mm:ss'
                        },
                      },
                      legend: {
                        show: true,
                        position: 'top',
                        horizontalAlign: 'left',
                      },
                      colors: oscillators.map(osc => osc.color || '#465FFF')
                    }}
                    series={oscillators}
                    type="line"
                    height={250}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
