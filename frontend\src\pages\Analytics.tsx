import PageMeta from "../components/common/PageMeta";
import SentimentAnalysisChart from "../components/ecommerce/SentimentAnalysisChart";
import PlatformSentimentComparison from "../components/ecommerce/PlatformSentimentComparison";
import CorrelationAnalysisChart from "../components/ecommerce/CorrelationAnalysisChart";
import HashRateCorrelationChart from "../components/ecommerce/HashRateCorrelationChart";
import M2CorrelationChart from "../components/ecommerce/M2CorrelationChart";
import GoldCorrelationChart from "../components/ecommerce/GoldCorrelationChart";
import CombinedCorrelationChart from "../components/ecommerce/CombinedCorrelationChart";

const Analytics: React.FC = () => {

  return (
    <>
      <PageMeta
        title="Analytics | Bitcoin Market Analyzer"
        description="Bitcoin sentiment analysis, correlation analysis, and market analytics"
      />
      <div className="grid grid-cols-12 gap-4 md:gap-6">
        <div className="col-span-12">
          <CombinedCorrelationChart />
        </div>
        <div className="col-span-12">
          <CorrelationAnalysisChart />
        </div>
        <div className="col-span-12">
          <M2CorrelationChart />
        </div>
        <div className="col-span-12">
          <GoldCorrelationChart />
        </div>
        <div className="col-span-12">
          <HashRateCorrelationChart />
        </div>
        <div className="col-span-12">
          <SentimentAnalysisChart />
        </div>
        <div className="col-span-12">
          <PlatformSentimentComparison />
        </div>
      </div>
    </>
  );
};

export default Analytics;
