import React, { useState, useEffect } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import axios from 'axios';

interface CorrelationMetric {
  name: string;
  correlation: number | null;
  color: string;
  significance?: string;
}

interface CombinedCorrelationData {
  federalFunds: number | null;
  m2: number | null;
  gold: number | null;
  hashRate: number | null;
  sentiment: number | null;
}

const CombinedCorrelationChart: React.FC = () => {
  const [correlationData, setCorrelationData] = useState<CombinedCorrelationData>({
    federalFunds: null,
    m2: null,
    gold: null,
    hashRate: null,
    sentiment: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState(365);

  const periodOptions = [
    { value: 30, label: '1 Month' },
    { value: 90, label: '3 Months' },
    { value: 180, label: '6 Months' },
    { value: 365, label: '1 Year' },
    { value: 730, label: '2 Years' },
    { value: 1095, label: '3 Years' },
  ];

  const fetchAllCorrelations = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch all correlation data in parallel
      const [fedResponse, m2Response, goldResponse, hashRateResponse, sentimentResponse] = await Promise.allSettled([
        axios.get(`http://localhost:8000/api/correlation-analysis/?days=${selectedPeriod}`),
        axios.get(`http://localhost:8000/api/m2-correlation/?days=${selectedPeriod}`),
        axios.get(`http://localhost:8000/api/gold-correlation/?days=${selectedPeriod}`),
        axios.get(`http://localhost:8000/api/hash-rate-correlation/?days=${selectedPeriod}`),
        axios.get(`http://localhost:8000/api/sentiment-analysis/?days=${selectedPeriod}`),
      ]);

      const newCorrelationData: CombinedCorrelationData = {
        federalFunds: null,
        m2: null,
        gold: null,
        hashRate: null,
        sentiment: null,
      };

      // Extract correlation values from responses
      if (fedResponse.status === 'fulfilled' && fedResponse.value.data.correlation_analysis) {
        newCorrelationData.federalFunds = fedResponse.value.data.correlation_analysis.correlation;
      }

      if (m2Response.status === 'fulfilled' && m2Response.value.data.correlation_analysis) {
        newCorrelationData.m2 = m2Response.value.data.correlation_analysis.correlation;
      }

      if (goldResponse.status === 'fulfilled' && goldResponse.value.data.correlation_analysis) {
        newCorrelationData.gold = goldResponse.value.data.correlation_analysis.correlation;
      }

      if (hashRateResponse.status === 'fulfilled' && hashRateResponse.value.data.correlation_analysis) {
        newCorrelationData.hashRate = hashRateResponse.value.data.correlation_analysis.correlation;
      }

      if (sentimentResponse.status === 'fulfilled' && sentimentResponse.value.data.summary) {
        newCorrelationData.sentiment = sentimentResponse.value.data.summary.price_correlation;
      }

      setCorrelationData(newCorrelationData);
    } catch (err: any) {
      console.error('Error fetching combined correlation data:', err);
      setError('Failed to fetch correlation data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllCorrelations();
  }, [selectedPeriod]);

  const getCorrelationColor = (correlation: number | null): string => {
    if (correlation === null) return '#9CA3AF';
    if (correlation > 0.7) return '#10B981'; // Strong positive - green
    if (correlation > 0.3) return '#34D399'; // Moderate positive - light green
    if (correlation > -0.3) return '#6B7280'; // Weak - gray
    if (correlation > -0.7) return '#F59E0B'; // Moderate negative - orange
    return '#EF4444'; // Strong negative - red
  };

  const getCorrelationDescription = (correlation: number | null): string => {
    if (correlation === null) return 'No Data';
    if (correlation > 0.7) return 'Strong Positive';
    if (correlation > 0.3) return 'Moderate Positive';
    if (correlation > -0.3) return 'Weak';
    if (correlation > -0.7) return 'Moderate Negative';
    return 'Strong Negative';
  };

  // Prepare chart data
  const metrics: CorrelationMetric[] = [
    {
      name: 'Federal Funds Rate',
      correlation: correlationData.federalFunds,
      color: '#3B82F6',
    },
    {
      name: 'M2 Money Supply',
      correlation: correlationData.m2,
      color: '#8B5CF6',
    },
    {
      name: 'Gold Price',
      correlation: correlationData.gold,
      color: '#F59E0B',
    },
    {
      name: 'Hash Rate',
      correlation: correlationData.hashRate,
      color: '#10B981',
    },
    {
      name: 'Market Sentiment',
      correlation: correlationData.sentiment,
      color: '#EC4899',
    },
  ];

  const chartSeries = [{
    name: 'Correlation with Bitcoin',
    data: metrics.map(metric => ({
      x: metric.name,
      y: metric.correlation || 0,
      fillColor: getCorrelationColor(metric.correlation)
    }))
  }];

  const chartOptions: ApexOptions = {
    chart: {
      type: 'bar',
      height: 400,
      toolbar: {
        show: true,
        tools: {
          download: true,
          selection: false,
          zoom: false,
          zoomin: false,
          zoomout: false,
          pan: false,
          reset: false
        }
      }
    },
    colors: metrics.map(metric => getCorrelationColor(metric.correlation)),
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '60%',
        distributed: true,
        dataLabels: {
          position: 'top'
        }
      }
    },
    dataLabels: {
      enabled: true,
      formatter: (val: number) => val.toFixed(3),
      offsetY: -20,
      style: {
        fontSize: '12px',
        colors: ['#304758']
      }
    },
    xaxis: {
      categories: metrics.map(metric => metric.name),
      labels: {
        style: {
          fontSize: '12px'
        },
        rotate: -45
      }
    },
    yaxis: {
      min: -1,
      max: 1,
      title: {
        text: 'Correlation Coefficient'
      },
      labels: {
        formatter: (val: number) => val.toFixed(2)
      }
    },
    grid: {
      borderColor: '#E5E7EB',
      yaxis: {
        lines: {
          show: true
        }
      }
    },
    tooltip: {
      y: {
        formatter: (val: number, opts) => {
          const metric = metrics[opts.dataPointIndex];
          return `${val.toFixed(3)} (${getCorrelationDescription(metric.correlation)})`;
        }
      }
    },
    legend: {
      show: false
    }
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white px-5 pb-5 pt-5 dark:border-gray-800 dark:bg-white/[0.03] sm:px-6 sm:pt-6">
      {/* Header */}
      <div className="flex flex-col gap-5 mb-6 sm:flex-row sm:justify-between">
        <div className="w-full">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
            Bitcoin Correlation Overview
          </h3>
          <p className="mt-1 text-gray-500 text-theme-sm dark:text-gray-400">
            Correlation coefficients between Bitcoin price and key economic indicators
          </p>
        </div>

        {/* Time Period Selector */}
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Time Period:
          </label>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Loading/Error States */}
      {isLoading && (
        <div className="flex items-center justify-center h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {error && (
        <div className="flex items-center justify-center h-[400px] text-red-500">
          {error}
        </div>
      )}

      {/* Chart */}
      {!isLoading && !error && (
        <>
          <div className="max-w-full overflow-x-auto">
            <Chart
              options={chartOptions}
              series={chartSeries}
              type="bar"
              height={400}
            />
          </div>

          {/* Correlation Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mt-6">
            {metrics.map((metric, index) => (
              <div key={index} className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                <div className="text-xs font-medium text-gray-500 mb-1">{metric.name}</div>
                <div
                  className="text-lg font-bold"
                  style={{ color: getCorrelationColor(metric.correlation) }}
                >
                  {metric.correlation !== null ? metric.correlation.toFixed(3) : 'N/A'}
                </div>
                <div className="text-sm text-gray-600">
                  {getCorrelationDescription(metric.correlation)}
                </div>
              </div>
            ))}
          </div>

          {/* Interpretation Guide */}
          <div className="mt-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2">
              Correlation Interpretation
            </h4>
            <div className="text-sm text-blue-700 dark:text-blue-300 grid grid-cols-1 md:grid-cols-2 gap-2">
              <div><strong>+1.0 to +0.7:</strong> Strong positive correlation</div>
              <div><strong>+0.7 to +0.3:</strong> Moderate positive correlation</div>
              <div><strong>+0.3 to -0.3:</strong> Weak correlation</div>
              <div><strong>-0.3 to -0.7:</strong> Moderate negative correlation</div>
              <div><strong>-0.7 to -1.0:</strong> Strong negative correlation</div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CombinedCorrelationChart;
